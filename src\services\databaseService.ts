import type { AppSettings, WeeklyRegistration } from '../types';

// Database structure interface
export interface DatabaseSchema {
  settings: AppSettings;
  registrations: WeeklyRegistration[];
  metadata: {
    version: string;
    createdAt: string;
    lastUpdated: string;
    totalRegistrations: number;
    totalPlayers: number;
  };
}

// Local storage key for our JSON database
const DB_KEY = 'badminton_json_database';

// Default database structure
const DEFAULT_DATABASE: DatabaseSchema = {
  settings: {
    courtsCount: 2,
    playersPerCourt: 4,
    extraCourtFee: 100000
  },
  registrations: [],
  metadata: {
    version: '1.0.0',
    createdAt: new Date().toISOString(),
    lastUpdated: new Date().toISOString(),
    totalRegistrations: 0,
    totalPlayers: 0
  }
};

/**
 * Database Service - Giả lập database JSON
 */
export class DatabaseService {
  
  /**
   * Khởi tạo database nếu chưa tồn tại
   */
  static initializeDatabase(): void {
    const existingDb = localStorage.getItem(DB_KEY);
    if (!existingDb) {
      localStorage.setItem(DB_KEY, JSON.stringify(DEFAULT_DATABASE));
    }
  }

  /**
   * Đ<PERSON>c toàn bộ database
   */
  static readDatabase(): DatabaseSchema {
    const dbString = localStorage.getItem(DB_KEY);
    if (!dbString) {
      this.initializeDatabase();
      return DEFAULT_DATABASE;
    }

    try {
      const db = JSON.parse(dbString);
      // Convert date strings back to Date objects for registrations
      if (db.registrations) {
        db.registrations = db.registrations.map((reg: any) => ({
          ...reg,
          weekStart: new Date(reg.weekStart),
          weekEnd: new Date(reg.weekEnd),
          players: reg.players.map((player: any) => ({
            ...player,
            registeredAt: new Date(player.registeredAt)
          }))
        }));
      }
      return db;
    } catch (error) {
      console.error('Error reading database:', error);
      this.initializeDatabase();
      return DEFAULT_DATABASE;
    }
  }

  /**
   * Ghi toàn bộ database
   */
  static writeDatabase(database: DatabaseSchema): void {
    try {
      // Update metadata
      database.metadata.lastUpdated = new Date().toISOString();
      database.metadata.totalRegistrations = database.registrations.length;
      database.metadata.totalPlayers = database.registrations.reduce(
        (total, reg) => total + reg.players.length, 0
      );

      localStorage.setItem(DB_KEY, JSON.stringify(database));
    } catch (error) {
      console.error('Error writing database:', error);
      throw new Error('Không thể lưu dữ liệu vào database');
    }
  }

  /**
   * Lấy settings
   */
  static getSettings(): AppSettings {
    const db = this.readDatabase();
    return db.settings;
  }

  /**
   * Cập nhật settings
   */
  static updateSettings(newSettings: AppSettings): void {
    const db = this.readDatabase();
    db.settings = newSettings;
    this.writeDatabase(db);
  }

  /**
   * Lấy tất cả registrations
   */
  static getRegistrations(): WeeklyRegistration[] {
    const db = this.readDatabase();
    return db.registrations;
  }

  /**
   * Thêm registration mới
   */
  static addRegistration(registration: WeeklyRegistration): void {
    const db = this.readDatabase();
    db.registrations.push(registration);
    this.writeDatabase(db);
  }

  /**
   * Xóa registration theo ID
   */
  static deleteRegistration(id: string): void {
    const db = this.readDatabase();
    db.registrations = db.registrations.filter(reg => reg.id !== id);
    this.writeDatabase(db);
  }

  /**
   * Tìm registration theo ID
   */
  static findRegistrationById(id: string): WeeklyRegistration | null {
    const db = this.readDatabase();
    return db.registrations.find(reg => reg.id === id) || null;
  }

  /**
   * Cập nhật registration
   */
  static updateRegistration(id: string, updatedRegistration: WeeklyRegistration): void {
    const db = this.readDatabase();
    const index = db.registrations.findIndex(reg => reg.id === id);
    if (index !== -1) {
      db.registrations[index] = updatedRegistration;
      this.writeDatabase(db);
    }
  }

  /**
   * Lấy metadata
   */
  static getMetadata() {
    const db = this.readDatabase();
    return db.metadata;
  }

  /**
   * Export database ra file JSON
   */
  static exportToFile(): void {
    const db = this.readDatabase();
    const jsonString = JSON.stringify(db, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `badminton-database-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  /**
   * Import database từ file JSON
   */
  static async importFromFile(file: File): Promise<void> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const jsonString = event.target?.result as string;
          const importedDb = JSON.parse(jsonString) as DatabaseSchema;
          
          // Validate structure
          if (!importedDb.settings || !importedDb.registrations || !importedDb.metadata) {
            throw new Error('Invalid database structure');
          }
          
          // Write to localStorage
          this.writeDatabase(importedDb);
          resolve();
        } catch (error) {
          reject(new Error('Invalid JSON file: ' + (error as Error).message));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsText(file);
    });
  }

  /**
   * Reset database về trạng thái mặc định
   */
  static resetDatabase(): void {
    localStorage.setItem(DB_KEY, JSON.stringify(DEFAULT_DATABASE));
  }

  /**
   * Lấy thống kê database
   */
  static getStats() {
    const db = this.readDatabase();
    return {
      totalRegistrations: db.registrations.length,
      totalPlayers: db.registrations.reduce((total, reg) => total + reg.players.length, 0),
      lastUpdated: db.metadata.lastUpdated,
      databaseSize: JSON.stringify(db).length
    };
  }
}
