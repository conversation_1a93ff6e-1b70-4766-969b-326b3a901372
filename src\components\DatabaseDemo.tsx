import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, message, Divider } from 'antd';
import { DatabaseOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { DatabaseService } from '../services/databaseService';
import type { WeeklyRegistration, Player } from '../types';

const { Title, Text, Paragraph } = Typography;

const DatabaseDemo: React.FC = () => {
  const [stats, setStats] = useState<any>(null);

  const refreshStats = () => {
    const dbStats = DatabaseService.getStats();
    const metadata = DatabaseService.getMetadata();
    setStats({ ...dbStats, ...metadata });
  };

  useEffect(() => {
    refreshStats();
  }, []);

  const createSampleData = () => {
    try {
      // Create sample registration
      const sampleRegistration: WeeklyRegistration = {
        id: Date.now().toString(),
        weekStart: new Date(),
        weekEnd: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        players: [
          {
            id: '1',
            name: '<PERSON><PERSON><PERSON><PERSON>',
            registeredAt: new Date()
          },
          {
            id: '2', 
            name: 'Trần Thị B',
            registeredAt: new Date()
          },
          {
            id: '3',
            name: 'Lê Văn C', 
            registeredAt: new Date()
          }
        ] as Player[],
        settings: {
          courtsCount: 2,
          playersPerCourt: 4,
          extraCourtFee: 100000
        }
      };

      DatabaseService.addRegistration(sampleRegistration);
      refreshStats();
      message.success('Đã tạo dữ liệu mẫu thành công!');
    } catch (error) {
      message.error('Lỗi khi tạo dữ liệu mẫu: ' + (error as Error).message);
    }
  };

  return (
    <Card 
      title={
        <Space>
          <DatabaseOutlined />
          <span>Demo Database JSON</span>
        </Space>
      }
      style={{ marginBottom: '16px' }}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <Title level={4}>Thống kê Database hiện tại</Title>
          {stats && (
            <div style={{ background: '#f9f9f9', padding: '16px', borderRadius: '8px' }}>
              <Space direction="vertical" size="small">
                <Text>📊 Tổng đăng ký: <strong>{stats.totalRegistrations}</strong></Text>
                <Text>👥 Tổng người chơi: <strong>{stats.totalPlayers}</strong></Text>
                <Text>💾 Kích thước database: <strong>{Math.round(stats.databaseSize / 1024)} KB</strong></Text>
                <Text>🔖 Phiên bản: <strong>{stats.version}</strong></Text>
                <Text>🕒 Cập nhật lần cuối: <strong>{new Date(stats.lastUpdated).toLocaleString('vi-VN')}</strong></Text>
              </Space>
            </div>
          )}
        </div>

        <Divider />

        <div>
          <Title level={4}>Thao tác Demo</Title>
          <Paragraph type="secondary">
            Các thao tác này sẽ giúp bạn test chức năng database JSON.
          </Paragraph>
          
          <Space wrap>
            <Button 
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={createSampleData}
            >
              Tạo dữ liệu mẫu
            </Button>
            
            <Button 
              onClick={refreshStats}
            >
              Refresh thống kê
            </Button>
            
            <Button 
              onClick={() => {
                DatabaseService.exportToFile();
                message.success('Đã xuất database!');
              }}
            >
              Xuất Database
            </Button>
            
            <Button 
              danger
              onClick={() => {
                DatabaseService.resetDatabase();
                refreshStats();
                message.success('Đã reset database!');
              }}
            >
              Reset Database
            </Button>
          </Space>
        </div>

        <Divider />

        <div>
          <Title level={4}>💡 Cách hoạt động</Title>
          <ul style={{ paddingLeft: '20px' }}>
            <li><strong>Database JSON ảo:</strong> Sử dụng localStorage để giả lập database JSON</li>
            <li><strong>Cấu trúc database:</strong> Bao gồm settings, registrations và metadata</li>
            <li><strong>CRUD operations:</strong> Hỗ trợ Create, Read, Update, Delete</li>
            <li><strong>Auto-save:</strong> Tự động lưu sau mỗi thao tác</li>
            <li><strong>Export/Import:</strong> Có thể xuất ra file JSON và nhập lại</li>
            <li><strong>Metadata tracking:</strong> Theo dõi thời gian cập nhật, số lượng records</li>
          </ul>
        </div>
      </Space>
    </Card>
  );
};

export default DatabaseDemo;
