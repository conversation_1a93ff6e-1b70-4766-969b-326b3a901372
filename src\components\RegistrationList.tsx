import React from 'react';
import { Card, List, Typography, Tag, Button, Popconfirm, Row, Col, Statistic, Empty } from 'antd';
import { DeleteOutlined, UnorderedListOutlined, UserOutlined, HomeOutlined, DollarOutlined, OrderedListOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { WeeklyRegistration, RegistrationSummary } from '../types';

const { Title, Text } = Typography;

interface RegistrationListProps {
  registrations: WeeklyRegistration[];
  onDeleteRegistration: (id: string) => void;
}

const RegistrationList: React.FC<RegistrationListProps> = ({
  registrations,
  onDeleteRegistration
}) => {
  const formatDate = (date: Date) => {
    return dayjs(date).format('DD/MM/YYYY');
  };

  const calculateSummary = (registration: WeeklyRegistration): RegistrationSummary => {
    const totalPlayers = registration.players.length;
    const maxPlayersWithDefaultCourts = registration.settings.courtsCount * registration.settings.playersPerCourt;
    const extraPlayersCount = Math.max(0, totalPlayers - maxPlayersWithDefaultCourts);
    const extraCourts = Math.ceil(extraPlayersCount / registration.settings.playersPerCourt);
    const requiredCourts = registration.settings.courtsCount + extraCourts;
    const totalExtraFee = extraCourts * registration.settings.extraCourtFee;
    const feePerExtraPlayer = extraPlayersCount > 0 ? totalExtraFee / extraPlayersCount : 0;

    return {
      totalPlayers,
      requiredCourts,
      extraCourts,
      extraPlayersCount,
      totalExtraFee,
      feePerExtraPlayer
    };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  if (registrations.length === 0) {
    return (
      <Card className="fade-in-up">
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
          gap: '16px'
        }}>
          <div style={{
            width: '44px',
            height: '44px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
            borderRadius: '12px',
            color: 'white',
            fontSize: '22px',
            boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
          }}>
            <OrderedListOutlined />
          </div>
          <Title level={2} className="mb-0" style={{
            color: '#1890ff',
            fontSize: '26px',
            fontWeight: 600,
            lineHeight: 1.2,
            marginTop: '2px'
          }}>
            Danh sách đăng ký
          </Title>
        </div>
        <Empty
          description="Chưa có đăng ký nào"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ padding: '40px 0' }}
        />
      </Card>
    );
  }

  return (
    <Card className="fade-in-up">
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '24px',
        gap: '16px'
      }}>
        <div style={{
          width: '44px',
          height: '44px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
          borderRadius: '12px',
          color: 'white',
          fontSize: '22px',
          boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
        }}>
          <OrderedListOutlined />
        </div>
        <Title level={2} className="mb-0" style={{
          color: '#1890ff',
          fontSize: '26px',
          fontWeight: 600,
          lineHeight: 1.2,
          marginTop: '2px'
        }}>
          Danh sách đăng ký theo tuần
        </Title>
      </div>

      <List
        itemLayout="vertical"
        dataSource={registrations}
        renderItem={(registration) => {
          const summary = calculateSummary(registration);

          return (
            <List.Item
              key={registration.id}
              actions={[
                <Popconfirm
                  title="Xóa đăng ký"
                  description="Bạn có chắc chắn muốn xóa đăng ký này?"
                  onConfirm={() => onDeleteRegistration(registration.id)}
                  okText="Xóa"
                  cancelText="Hủy"
                >
                  <Button danger icon={<DeleteOutlined />}>
                    Xóa
                  </Button>
                </Popconfirm>
              ]}
            >
              <Card size="small">
                <div className="mb-4">
                  <Title level={4} className="mb-1">
                    Tuần {formatDate(registration.weekStart)} - {formatDate(registration.weekEnd)}
                  </Title>
                  <Tag color="blue" icon={<UserOutlined />}>
                    {registration.players.length} người đăng ký
                  </Tag>
                </div>

                <Row gutter={[16, 16]}>
                  <Col xs={24} md={12}>
                    <Title level={5}>Danh sách người chơi:</Title>
                    <div style={{ maxHeight: 150, overflow: 'auto' }}>
                      <Row gutter={[8, 8]}>
                        {registration.players.map((player, index) => (
                          <Col key={player.id} xs={12} sm={8} md={12}>
                            <Text className="text-sm">
                              {index + 1}. {player.name}
                            </Text>
                          </Col>
                        ))}
                      </Row>
                    </div>
                  </Col>

                  <Col xs={24} md={12}>
                    <Title level={5}>Thống kê:</Title>
                    <Row gutter={[8, 8]}>
                      <Col span={12}>
                        <Statistic
                          title="Số sân cần thiết"
                          value={summary.requiredCourts}
                          prefix={<HomeOutlined />}
                          suffix="sân"
                          valueStyle={{ fontSize: '16px' }}
                        />
                      </Col>

                      {summary.extraCourts > 0 && (
                        <>
                          <Col span={12}>
                            <Statistic
                              title="Sân thêm"
                              value={summary.extraCourts}
                              prefix={<HomeOutlined />}
                              suffix="sân"
                              valueStyle={{ fontSize: '16px', color: '#fa8c16' }}
                            />
                          </Col>
                          <Col span={12}>
                            <Statistic
                              title="Phí thêm"
                              value={formatCurrency(summary.totalExtraFee)}
                              prefix={<DollarOutlined />}
                              valueStyle={{ fontSize: '14px', color: '#f5222d' }}
                            />
                          </Col>
                          <Col span={12}>
                            <Statistic
                              title="Phí/người vượt"
                              value={formatCurrency(summary.feePerExtraPlayer)}
                              prefix={<DollarOutlined />}
                              valueStyle={{ fontSize: '14px', color: '#f5222d' }}
                            />
                          </Col>
                        </>
                      )}

                      {summary.extraCourts === 0 && (
                        <Col span={24}>
                          <Tag color="success" className="mt-2">
                            ✅ Không cần thuê thêm sân
                          </Tag>
                        </Col>
                      )}
                    </Row>
                  </Col>
                </Row>
              </Card>
            </List.Item>
          );
        }}
      />
    </Card>
  );
};

export default RegistrationList;
