import React, { useState, useMemo } from 'react';
import {
  Card,
  List,
  Typography,
  Tag,
  Button,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Empty,
  Select,
  Space,
  DatePicker,
  Divider,
  Alert
} from 'antd';
import {
  DeleteOutlined,
  UnorderedListOutlined,
  UserOutlined,
  HomeOutlined,
  DollarOutlined,
  OrderedListOutlined,
  CalendarOutlined,
  FilterOutlined,
  Bar<PERSON>hartOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import type { WeeklyRegistration, RegistrationSummary } from '../types';

dayjs.extend(weekOfYear);

const { Title, Text } = Typography;

interface RegistrationListProps {
  registrations: WeeklyRegistration[];
  onDeleteRegistration: (id: string) => void;
}

const RegistrationList: React.FC<RegistrationListProps> = ({
  registrations,
  onDeleteRegistration
}) => {
  const [filterType, setFilterType] = useState<'all' | 'week' | 'month' | 'year'>('all');
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);

  const formatDate = (date: Date) => {
    return dayjs(date).format('DD/MM/YYYY');
  };

  // Calculate summary for a registration
  const calculateSummary = (registration: WeeklyRegistration): RegistrationSummary => {
    const totalPlayers = registration.players.length;
    const maxPlayersWithDefaultCourts = registration.settings.courtsCount * registration.settings.playersPerCourt;
    const extraPlayersCount = Math.max(0, totalPlayers - maxPlayersWithDefaultCourts);
    const extraCourts = Math.ceil(extraPlayersCount / registration.settings.playersPerCourt);
    const requiredCourts = registration.settings.courtsCount + extraCourts;
    const totalExtraFee = extraCourts * registration.settings.extraCourtFee;
    const feePerExtraPlayer = extraPlayersCount > 0 ? totalExtraFee / extraPlayersCount : 0;

    return {
      totalPlayers,
      requiredCourts,
      extraCourts,
      extraPlayersCount,
      totalExtraFee,
      feePerExtraPlayer
    };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Filter registrations based on selected criteria
  const filteredRegistrations = useMemo(() => {
    if (filterType === 'all' || !selectedDate) {
      return registrations.sort((a, b) => new Date(b.weekStart).getTime() - new Date(a.weekStart).getTime());
    }

    return registrations.filter(registration => {
      const regDate = dayjs(registration.weekStart);

      switch (filterType) {
        case 'week':
          return regDate.week() === selectedDate.week() && regDate.year() === selectedDate.year();
        case 'month':
          return regDate.month() === selectedDate.month() && regDate.year() === selectedDate.year();
        case 'year':
          return regDate.year() === selectedDate.year();
        default:
          return true;
      }
    }).sort((a, b) => new Date(b.weekStart).getTime() - new Date(a.weekStart).getTime());
  }, [registrations, filterType, selectedDate]);

  // Calculate overall statistics
  const overallStats = useMemo(() => {
    const totalRegistrations = filteredRegistrations.length;
    const totalPlayers = filteredRegistrations.reduce((sum, reg) => sum + reg.players.length, 0);
    const totalExtraFee = filteredRegistrations.reduce((sum, reg) => {
      const summary = calculateSummary(reg);
      return sum + summary.totalExtraFee;
    }, 0);
    const totalExtraCourts = filteredRegistrations.reduce((sum, reg) => {
      const summary = calculateSummary(reg);
      return sum + summary.extraCourts;
    }, 0);

    return {
      totalRegistrations,
      totalPlayers,
      totalExtraFee,
      totalExtraCourts,
      averagePlayersPerWeek: totalRegistrations > 0 ? Math.round(totalPlayers / totalRegistrations) : 0
    };
  }, [filteredRegistrations]);

  // Render filter controls
  const renderFilterControls = () => (
    <Card size="small" style={{ marginBottom: '16px' }}>
      <Space wrap>
        <Space>
          <FilterOutlined />
          <Text strong>Lọc theo:</Text>
        </Space>

        <Select
          value={filterType}
          onChange={setFilterType}
          style={{ width: 120 }}
          options={[
            { label: 'Tất cả', value: 'all' },
            { label: 'Tuần', value: 'week' },
            { label: 'Tháng', value: 'month' },
            { label: 'Năm', value: 'year' }
          ]}
        />

        {filterType !== 'all' && (
          <DatePicker
            value={selectedDate}
            onChange={setSelectedDate}
            picker={filterType === 'week' ? 'week' : filterType === 'month' ? 'month' : 'year'}
            placeholder={`Chọn ${filterType === 'week' ? 'tuần' : filterType === 'month' ? 'tháng' : 'năm'}`}
            style={{ width: 150 }}
          />
        )}

        {(filterType !== 'all' && selectedDate) && (
          <Button
            size="small"
            onClick={() => {
              setFilterType('all');
              setSelectedDate(null);
            }}
          >
            Xóa bộ lọc
          </Button>
        )}
      </Space>
    </Card>
  );

  // Render overall statistics
  const renderOverallStats = () => (
    <Card size="small" style={{ marginBottom: '16px' }}>
      <Row gutter={16}>
        <Col xs={12} sm={6}>
          <Statistic
            title="Tổng đăng ký"
            value={overallStats.totalRegistrations}
            prefix={<BarChartOutlined />}
            valueStyle={{ fontSize: '18px', color: '#1890ff' }}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="Tổng người chơi"
            value={overallStats.totalPlayers}
            prefix={<UserOutlined />}
            valueStyle={{ fontSize: '18px', color: '#52c41a' }}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="TB người/tuần"
            value={overallStats.averagePlayersPerWeek}
            prefix={<UserOutlined />}
            valueStyle={{ fontSize: '18px', color: '#722ed1' }}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="Tổng phí thêm"
            value={formatCurrency(overallStats.totalExtraFee)}
            prefix={<DollarOutlined />}
            valueStyle={{ fontSize: '16px', color: '#f5222d' }}
          />
        </Col>
      </Row>
    </Card>
  );

  if (registrations.length === 0) {
    return (
      <Card className="fade-in-up">
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
          gap: '16px'
        }}>
          <div style={{
            width: '44px',
            height: '44px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
            borderRadius: '12px',
            color: 'white',
            fontSize: '22px',
            boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
          }}>
            <OrderedListOutlined />
          </div>
          <Title level={2} className="mb-0" style={{
            color: '#1890ff',
            fontSize: '26px',
            fontWeight: 600,
            lineHeight: 1.2,
            marginTop: '2px'
          }}>
            Danh sách đăng ký
          </Title>
        </div>
        <Empty
          description="Chưa có đăng ký nào"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ padding: '40px 0' }}
        />
      </Card>
    );
  }

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Card className="fade-in-up">
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
          gap: '16px'
        }}>
          <div style={{
            width: '44px',
            height: '44px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
            borderRadius: '12px',
            color: 'white',
            fontSize: '22px',
            boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
          }}>
            <OrderedListOutlined />
          </div>
          <Title level={2} className="mb-0" style={{
            color: '#1890ff',
            fontSize: '26px',
            fontWeight: 600,
            lineHeight: 1.2,
            marginTop: '2px'
          }}>
            Danh sách đăng ký theo tuần
          </Title>
        </div>

        {/* Filter Controls */}
        {renderFilterControls()}

        {/* Overall Statistics */}
        {renderOverallStats()}

        {/* Show filtered results info */}
        {filterType !== 'all' && (
          <Alert
            message={`Hiển thị ${filteredRegistrations.length} đăng ký được lọc từ tổng ${registrations.length} đăng ký`}
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        {/* No filtered results */}
        {filteredRegistrations.length === 0 && filterType !== 'all' && (
          <Empty
            description="Không tìm thấy đăng ký nào phù hợp với bộ lọc"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            style={{ padding: '40px 0' }}
          />
        )}

        {/* Registration List */}
        {filteredRegistrations.length > 0 && (
          <List
            itemLayout="vertical"
            dataSource={filteredRegistrations}
            renderItem={(registration) => {
              const summary = calculateSummary(registration);

              return (
                <List.Item
                  key={registration.id}
                  actions={[
                    <Popconfirm
                      title="Xóa đăng ký"
                      description="Bạn có chắc chắn muốn xóa đăng ký này?"
                      onConfirm={() => onDeleteRegistration(registration.id)}
                      okText="Xóa"
                      cancelText="Hủy"
                    >
                      <Button danger icon={<DeleteOutlined />}>
                        Xóa
                      </Button>
                    </Popconfirm>
                  ]}
                >
                  <Card size="small">
                    <div className="mb-4">
                      <Title level={4} className="mb-1">
                        Tuần {formatDate(registration.weekStart)} - {formatDate(registration.weekEnd)}
                      </Title>
                      <Tag color="blue" icon={<UserOutlined />}>
                        {registration.players.length} người đăng ký
                      </Tag>
                    </div>

                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={12}>
                        <Title level={5}>Danh sách người chơi:</Title>
                        <div style={{ maxHeight: 150, overflow: 'auto' }}>
                          <Row gutter={[8, 8]}>
                            {registration.players.map((player, index) => (
                              <Col key={player.id} xs={12} sm={8} md={12}>
                                <Text className="text-sm">
                                  {index + 1}. {player.name}
                                </Text>
                              </Col>
                            ))}
                          </Row>
                        </div>
                      </Col>

                      <Col xs={24} md={12}>
                        <Title level={5}>Thống kê:</Title>
                        <Row gutter={[8, 8]}>
                          <Col span={12}>
                            <Statistic
                              title="Số sân cần thiết"
                              value={summary.requiredCourts}
                              prefix={<HomeOutlined />}
                              suffix="sân"
                              valueStyle={{ fontSize: '16px' }}
                            />
                          </Col>

                          {summary.extraCourts > 0 && (
                            <>
                              <Col span={12}>
                                <Statistic
                                  title="Sân thêm"
                                  value={summary.extraCourts}
                                  prefix={<HomeOutlined />}
                                  suffix="sân"
                                  valueStyle={{ fontSize: '16px', color: '#fa8c16' }}
                                />
                              </Col>
                              <Col span={12}>
                                <Statistic
                                  title="Phí thêm"
                                  value={formatCurrency(summary.totalExtraFee)}
                                  prefix={<DollarOutlined />}
                                  valueStyle={{ fontSize: '14px', color: '#f5222d' }}
                                />
                              </Col>
                              <Col span={12}>
                                <Statistic
                                  title="Phí/người vượt"
                                  value={formatCurrency(summary.feePerExtraPlayer)}
                                  prefix={<DollarOutlined />}
                                  valueStyle={{ fontSize: '14px', color: '#f5222d' }}
                                />
                              </Col>
                            </>
                          )}

                          {summary.extraCourts === 0 && (
                            <Col span={24}>
                              <Tag color="success" className="mt-2">
                                ✅ Không cần thuê thêm sân
                              </Tag>
                            </Col>
                          )}
                        </Row>
                      </Col>
                    </Row>
                  </Card>
                </List.Item>
              );
            }}
          />
        )}
      </Card>
    </Space>
  );
};

export default RegistrationList;
