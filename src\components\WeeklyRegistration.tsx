import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, List, Tag, Space, DatePicker, message } from 'antd';
import { PlusOutlined, DeleteOutlined, CalendarOutlined, UserOutlined, TrophyOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import type { WeeklyRegistration as WeeklyRegistrationType, Player, AppSettings } from '../types';
import CustomLabel from './CustomLabel';

dayjs.extend(weekOfYear);

const { Title } = Typography;

interface WeeklyRegistrationProps {
  settings: AppSettings;
  onRegistrationSubmit: (registration: WeeklyRegistrationType) => void;
}

const WeeklyRegistration: React.FC<WeeklyRegistrationProps> = ({
  settings,
  onRegistrationSubmit
}) => {
  const [selectedWeek, setSelectedWeek] = useState<dayjs.Dayjs | null>(null);
  const [playerName, setPlayerName] = useState<string>('');
  const [players, setPlayers] = useState<Player[]>([]);

  const getWeekDates = (weekDate: dayjs.Dayjs) => {
    const start = weekDate.startOf('week').add(1, 'day'); // Monday
    const end = weekDate.endOf('week').add(1, 'day'); // Sunday
    return { start: start.toDate(), end: end.toDate() };
  };

  const addPlayer = () => {
    if (playerName.trim()) {
      const newPlayer: Player = {
        id: Date.now().toString(),
        name: playerName.trim(),
        registeredAt: new Date()
      };
      setPlayers([...players, newPlayer]);
      setPlayerName('');
      message.success(`Đã thêm ${playerName.trim()}`);
    }
  };

  const removePlayer = (playerId: string) => {
    const player = players.find(p => p.id === playerId);
    setPlayers(players.filter(p => p.id !== playerId));
    if (player) {
      message.info(`Đã xóa ${player.name}`);
    }
  };

  const handleSubmit = () => {
    if (selectedWeek && players.length > 0) {
      const { start, end } = getWeekDates(selectedWeek);
      const registration: WeeklyRegistrationType = {
        id: Date.now().toString(),
        weekStart: start,
        weekEnd: end,
        players: players,
        settings: { ...settings }
      };
      onRegistrationSubmit(registration);
      setPlayers([]);
      setSelectedWeek(null);
      message.success('Đăng ký thành công!');
    }
  };

  return (
    <Card className="fade-in-up">
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '24px',
        gap: '16px'
      }}>
        <div style={{
          width: '44px',
          height: '44px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)',
          borderRadius: '12px',
          color: 'white',
          fontSize: '22px',
          boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
        }}>
          <TrophyOutlined />
        </div>
        <Title level={2} className="mb-0" style={{
          color: '#1890ff',
          fontSize: '26px',
          fontWeight: 600,
          lineHeight: 1.2,
          marginTop: '2px'
        }}>
          Đăng ký đánh cầu lông
        </Title>
      </div>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div>
          <CustomLabel icon={<CalendarOutlined />}>
            Chọn tuần
          </CustomLabel>
          <DatePicker.WeekPicker
            value={selectedWeek}
            onChange={setSelectedWeek}
            style={{ width: '100%' }}
            placeholder="Chọn tuần đăng ký"
            disabledDate={(current) => current && current < dayjs().startOf('week')}
          />
        </div>

        <div>
          <CustomLabel icon={<UserOutlined />}>
            Thêm người chơi
          </CustomLabel>
          <Space.Compact style={{ width: '100%' }}>
            <Input
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              placeholder="Nhập tên người chơi"
              prefix={<UserOutlined />}
              onPressEnter={addPlayer}
              size="large"
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addPlayer}
              disabled={!playerName.trim()}
              size="large"
            >
              Thêm
            </Button>
          </Space.Compact>
        </div>

        {players.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <Title level={4} className="mb-0">
                Danh sách đăng ký
              </Title>
              <Tag color="blue">{players.length} người</Tag>
            </div>
            <List
              size="small"
              bordered
              dataSource={players}
              style={{ maxHeight: 200, overflow: 'auto' }}
              renderItem={(player, index) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => removePlayer(player.id)}
                    >
                      Xóa
                    </Button>
                  ]}
                >
                  <span>{index + 1}. {player.name}</span>
                </List.Item>
              )}
            />
          </div>
        )}

        <Button
          type="primary"
          size="large"
          block
          onClick={handleSubmit}
          disabled={!selectedWeek || players.length === 0}
        >
          Đăng ký tuần này
        </Button>
      </Space>
    </Card>
  );
};

export default WeeklyRegistration;
