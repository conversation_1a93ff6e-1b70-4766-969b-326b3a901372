import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Upload, 
  Space, 
  Typography, 
  message, 
  Modal, 
  Divider,
  Alert 
} from 'antd';
import { 
  DownloadOutlined, 
  UploadOutlined, 
  ExclamationCircleOutlined,
  FileTextOutlined,
  SaveOutlined
} from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import type { AppSettings, WeeklyRegistration } from '../types';
import { 
  exportDataToJSON, 
  importDataFromJSON, 
  validateJSONFile,
  type AppData 
} from '../utils/fileUtils';

const { Title, Text, Paragraph } = Typography;
const { confirm } = Modal;

interface DataManagerProps {
  settings: AppSettings;
  registrations: WeeklyRegistration[];
  onDataImport: (settings: AppSettings, registrations: WeeklyRegistration[]) => void;
}

const DataManager: React.FC<DataManagerProps> = ({
  settings,
  registrations,
  onDataImport
}) => {
  const [uploading, setUploading] = useState(false);

  // Handle data export
  const handleExport = () => {
    try {
      exportDataToJSON(settings, registrations);
      message.success('Dữ liệu đã được xuất thành công!');
    } catch (error) {
      message.error('Lỗi khi xuất dữ liệu: ' + (error as Error).message);
    }
  };

  // Handle file upload
  const handleFileUpload = async (file: UploadFile) => {
    if (!file.originFileObj) {
      message.error('Không thể đọc file');
      return false;
    }

    // Validate file
    if (!validateJSONFile(file.originFileObj)) {
      message.error('File không hợp lệ. Vui lòng chọn file JSON có kích thước nhỏ hơn 10MB');
      return false;
    }

    setUploading(true);

    try {
      const importedData: AppData = await importDataFromJSON(file.originFileObj);
      
      // Show confirmation dialog
      confirm({
        title: 'Xác nhận nhập dữ liệu',
        icon: <ExclamationCircleOutlined />,
        content: (
          <div>
            <Paragraph>
              Bạn có chắc chắn muốn nhập dữ liệu từ file này không? 
              Điều này sẽ thay thế toàn bộ dữ liệu hiện tại.
            </Paragraph>
            <div style={{ background: '#f5f5f5', padding: '12px', borderRadius: '6px' }}>
              <Text strong>Thông tin file:</Text>
              <br />
              <Text>• Số lượng đăng ký: {importedData.registrations.length}</Text>
              <br />
              <Text>• Xuất lúc: {new Date(importedData.exportedAt).toLocaleString('vi-VN')}</Text>
              <br />
              <Text>• Phiên bản: {importedData.version}</Text>
            </div>
          </div>
        ),
        okText: 'Nhập dữ liệu',
        cancelText: 'Hủy',
        okType: 'primary',
        onOk: () => {
          onDataImport(importedData.settings, importedData.registrations);
          message.success('Dữ liệu đã được nhập thành công!');
        },
      });
    } catch (error) {
      message.error('Lỗi khi đọc file: ' + (error as Error).message);
    } finally {
      setUploading(false);
    }

    return false; // Prevent default upload behavior
  };

  const dataStats = {
    totalRegistrations: registrations.length,
    totalPlayers: registrations.reduce((sum, reg) => sum + reg.players.length, 0),
    lastRegistration: registrations.length > 0 
      ? registrations[registrations.length - 1].weekStart.toLocaleDateString('vi-VN')
      : 'Chưa có'
  };

  return (
    <Card title={
      <Space>
        <FileTextOutlined />
        <span>Quản lý dữ liệu</span>
      </Space>
    }>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Current Data Info */}
        <div>
          <Title level={4}>Thông tin dữ liệu hiện tại</Title>
          <div style={{ background: '#f9f9f9', padding: '16px', borderRadius: '8px' }}>
            <Space direction="vertical" size="small">
              <Text>📊 Tổng số đăng ký: <strong>{dataStats.totalRegistrations}</strong></Text>
              <Text>👥 Tổng số người chơi: <strong>{dataStats.totalPlayers}</strong></Text>
              <Text>📅 Đăng ký gần nhất: <strong>{dataStats.lastRegistration}</strong></Text>
              <Text>🏸 Số sân mặc định: <strong>{settings.courtsCount}</strong></Text>
              <Text>👤 Người chơi/sân: <strong>{settings.playersPerCourt}</strong></Text>
            </Space>
          </div>
        </div>

        <Divider />

        {/* Export Section */}
        <div>
          <Title level={4}>Xuất dữ liệu</Title>
          <Paragraph type="secondary">
            Xuất toàn bộ dữ liệu (cài đặt và đăng ký) ra file JSON để sao lưu hoặc chuyển sang thiết bị khác.
          </Paragraph>
          <Button 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={handleExport}
            size="large"
          >
            Xuất dữ liệu ra file JSON
          </Button>
        </div>

        <Divider />

        {/* Import Section */}
        <div>
          <Title level={4}>Nhập dữ liệu</Title>
          <Paragraph type="secondary">
            Nhập dữ liệu từ file JSON đã xuất trước đó. Dữ liệu hiện tại sẽ bị thay thế hoàn toàn.
          </Paragraph>
          
          <Alert
            message="Cảnh báo"
            description="Việc nhập dữ liệu sẽ thay thế toàn bộ dữ liệu hiện tại. Hãy chắc chắn bạn đã sao lưu dữ liệu quan trọng."
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <Upload
            accept=".json"
            beforeUpload={handleFileUpload}
            showUploadList={false}
            disabled={uploading}
          >
            <Button 
              icon={<UploadOutlined />}
              loading={uploading}
              size="large"
            >
              {uploading ? 'Đang xử lý...' : 'Chọn file JSON để nhập'}
            </Button>
          </Upload>
        </div>

        <Divider />

        {/* Tips */}
        <div>
          <Title level={4}>💡 Mẹo sử dụng</Title>
          <ul style={{ paddingLeft: '20px' }}>
            <li>Xuất dữ liệu thường xuyên để tạo bản sao lưu</li>
            <li>File JSON có thể mở bằng notepad để xem nội dung</li>
            <li>Có thể chia sẻ file JSON với người khác để chuyển dữ liệu</li>
            <li>Tên file sẽ bao gồm ngày xuất để dễ quản lý</li>
          </ul>
        </div>
      </Space>
    </Card>
  );
};

export default DataManager;
