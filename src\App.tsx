import React, { useState, useEffect } from 'react';
import { Layout, Tabs, Typography, Space, message } from 'antd';
import { CalendarOutlined, UnorderedListOutlined, SettingOutlined, FileTextOutlined } from '@ant-design/icons';
import type { AppSettings, WeeklyRegistration as WeeklyRegistrationType, RegistrationSummary } from './types';
import Settings from './components/Settings';
import WeeklyRegistration from './components/WeeklyRegistration';
import Summary from './components/Summary';
import RegistrationList from './components/RegistrationList';
import DataManager from './components/DataManager';
import { autoSaveToJSON } from './utils/fileUtils';

const { Header, Content } = Layout;
const { Title } = Typography;

const DEFAULT_SETTINGS: AppSettings = {
  courtsCount: 2,
  playersPerCourt: 4,
  extraCourtFee: 100000
};

function App() {
  const [activeTab, setActiveTab] = useState<string>('register');
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [registrations, setRegistrations] = useState<WeeklyRegistrationType[]>([]);
  const [currentSummary, setCurrentSummary] = useState<RegistrationSummary | null>(null);

  // Show welcome message on first load
  useEffect(() => {
    message.info({
      content: 'Chào mừng! Dữ liệu sẽ được tự động lưu vào file JSON sau mỗi lần đăng ký.',
      duration: 5,
    });
  }, []);

  // Handle settings change
  const handleSettingsChange = (newSettings: AppSettings) => {
    setSettings(newSettings);
    // Auto-save when settings change
    autoSaveToJSON(newSettings, registrations);
    message.success('Cài đặt đã được lưu vào file JSON!');
  };

  // Calculate summary for current week registration
  const calculateSummary = (registration: WeeklyRegistrationType): RegistrationSummary => {
    const totalPlayers = registration.players.length;
    const maxPlayersWithDefaultCourts = registration.settings.courtsCount * registration.settings.playersPerCourt;
    const extraPlayersCount = Math.max(0, totalPlayers - maxPlayersWithDefaultCourts);
    const extraCourts = Math.ceil(extraPlayersCount / registration.settings.playersPerCourt);
    const requiredCourts = registration.settings.courtsCount + extraCourts;
    const totalExtraFee = extraCourts * registration.settings.extraCourtFee;
    const feePerExtraPlayer = extraPlayersCount > 0 ? totalExtraFee / extraPlayersCount : 0;

    return {
      totalPlayers,
      requiredCourts,
      extraCourts,
      extraPlayersCount,
      totalExtraFee,
      feePerExtraPlayer
    };
  };

  // Handle new registration submission
  const handleRegistrationSubmit = (registration: WeeklyRegistrationType) => {
    const newRegistrations = [...registrations, registration];
    setRegistrations(newRegistrations);

    // Auto-save to JSON file
    autoSaveToJSON(settings, newRegistrations);

    // Calculate and show summary
    const summary = calculateSummary(registration);
    setCurrentSummary(summary);

    // Show success message
    message.success('Đăng ký thành công! File JSON đã được tự động tải xuống.');
  };

  // Handle registration deletion
  const handleDeleteRegistration = (id: string) => {
    const newRegistrations = registrations.filter(reg => reg.id !== id);
    setRegistrations(newRegistrations);
    localStorage.setItem(STORAGE_KEYS.REGISTRATIONS, JSON.stringify(newRegistrations));
  };

  // Handle data import from JSON file
  const handleDataImport = (newSettings: AppSettings, newRegistrations: WeeklyRegistrationType[]) => {
    setSettings(newSettings);
    setRegistrations(newRegistrations);

    // Save to localStorage
    localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(newSettings));
    localStorage.setItem(STORAGE_KEYS.REGISTRATIONS, JSON.stringify(newRegistrations));

    // Clear current summary
    setCurrentSummary(null);
  };

  const tabItems = [
    {
      key: 'register',
      label: (
        <span>
          <CalendarOutlined />
          Đăng ký
        </span>
      ),
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <WeeklyRegistration
            settings={settings}
            onRegistrationSubmit={handleRegistrationSubmit}
          />
          {currentSummary && <Summary summary={currentSummary} />}
        </Space>
      ),
    },
    {
      key: 'list',
      label: (
        <span>
          <UnorderedListOutlined />
          Danh sách
        </span>
      ),
      children: (
        <RegistrationList
          registrations={registrations}
          onDeleteRegistration={handleDeleteRegistration}
        />
      ),
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          Cài đặt
        </span>
      ),
      children: (
        <Settings
          settings={settings}
          onSettingsChange={handleSettingsChange}
        />
      ),
    },
    {
      key: 'data',
      label: (
        <span>
          <FileTextOutlined />
          Dữ liệu
        </span>
      ),
      children: (
        <DataManager
          settings={settings}
          registrations={registrations}
          onDataImport={handleDataImport}
        />
      ),
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <div className="flex items-center justify-center h-full">
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            🏸 Quản lý đăng ký cầu lông
          </Title>
        </div>
      </Header>

      <Content style={{ padding: '24px', background: '#f0f2f5' }}>
        <div style={{ maxWidth: 1200, margin: '0 auto' }}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            size="large"
            centered
          />
        </div>
      </Content>
    </Layout>
  );
}

export default App;
