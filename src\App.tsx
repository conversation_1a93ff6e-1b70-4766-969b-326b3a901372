import React, { useState, useEffect } from 'react';
import { Layout, Tabs, Typography, Space } from 'antd';
import { CalendarOutlined, UnorderedListOutlined, SettingOutlined } from '@ant-design/icons';
import type { AppSettings, WeeklyRegistration as WeeklyRegistrationType, RegistrationSummary } from './types';
import Settings from './components/Settings';
import WeeklyRegistration from './components/WeeklyRegistration';
import Summary from './components/Summary';
import RegistrationList from './components/RegistrationList';

const { Header, Content } = Layout;
const { Title } = Typography;

const STORAGE_KEYS = {
  SETTINGS: 'badminton-settings',
  REGISTRATIONS: 'badminton-registrations'
};

const DEFAULT_SETTINGS: AppSettings = {
  courtsCount: 2,
  playersPerCourt: 4,
  extraCourtFee: 100000
};

function App() {
  const [activeTab, setActiveTab] = useState<string>('register');
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [registrations, setRegistrations] = useState<WeeklyRegistrationType[]>([]);
  const [currentSummary, setCurrentSummary] = useState<RegistrationSummary | null>(null);

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem(STORAGE_KEYS.SETTINGS);
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }

    const savedRegistrations = localStorage.getItem(STORAGE_KEYS.REGISTRATIONS);
    if (savedRegistrations) {
      const parsed = JSON.parse(savedRegistrations);
      // Convert date strings back to Date objects
      const registrationsWithDates = parsed.map((reg: any) => ({
        ...reg,
        weekStart: new Date(reg.weekStart),
        weekEnd: new Date(reg.weekEnd),
        players: reg.players.map((player: any) => ({
          ...player,
          registeredAt: new Date(player.registeredAt)
        }))
      }));
      setRegistrations(registrationsWithDates);
    }
  }, []);

  // Save settings to localStorage
  const handleSettingsChange = (newSettings: AppSettings) => {
    setSettings(newSettings);
    localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(newSettings));
  };

  // Calculate summary for current week registration
  const calculateSummary = (registration: WeeklyRegistrationType): RegistrationSummary => {
    const totalPlayers = registration.players.length;
    const maxPlayersWithDefaultCourts = registration.settings.courtsCount * registration.settings.playersPerCourt;
    const extraPlayersCount = Math.max(0, totalPlayers - maxPlayersWithDefaultCourts);
    const extraCourts = Math.ceil(extraPlayersCount / registration.settings.playersPerCourt);
    const requiredCourts = registration.settings.courtsCount + extraCourts;
    const totalExtraFee = extraCourts * registration.settings.extraCourtFee;
    const feePerExtraPlayer = extraPlayersCount > 0 ? totalExtraFee / extraPlayersCount : 0;

    return {
      totalPlayers,
      requiredCourts,
      extraCourts,
      extraPlayersCount,
      totalExtraFee,
      feePerExtraPlayer
    };
  };

  // Handle new registration submission
  const handleRegistrationSubmit = (registration: WeeklyRegistrationType) => {
    const newRegistrations = [...registrations, registration];
    setRegistrations(newRegistrations);
    localStorage.setItem(STORAGE_KEYS.REGISTRATIONS, JSON.stringify(newRegistrations));

    // Calculate and show summary
    const summary = calculateSummary(registration);
    setCurrentSummary(summary);
  };

  // Handle registration deletion
  const handleDeleteRegistration = (id: string) => {
    const newRegistrations = registrations.filter(reg => reg.id !== id);
    setRegistrations(newRegistrations);
    localStorage.setItem(STORAGE_KEYS.REGISTRATIONS, JSON.stringify(newRegistrations));
  };

  const tabItems = [
    {
      key: 'register',
      label: (
        <span>
          <CalendarOutlined />
          Đăng ký
        </span>
      ),
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <WeeklyRegistration
            settings={settings}
            onRegistrationSubmit={handleRegistrationSubmit}
          />
          {currentSummary && <Summary summary={currentSummary} />}
        </Space>
      ),
    },
    {
      key: 'list',
      label: (
        <span>
          <UnorderedListOutlined />
          Danh sách
        </span>
      ),
      children: (
        <RegistrationList
          registrations={registrations}
          onDeleteRegistration={handleDeleteRegistration}
        />
      ),
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          Cài đặt
        </span>
      ),
      children: (
        <Settings
          settings={settings}
          onSettingsChange={handleSettingsChange}
        />
      ),
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <div className="flex items-center justify-center h-full">
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            🏸 Quản lý đăng ký cầu lông
          </Title>
        </div>
      </Header>

      <Content style={{ padding: '24px', background: '#f0f2f5' }}>
        <div style={{ maxWidth: 1200, margin: '0 auto' }}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            size="large"
            centered
          />
        </div>
      </Content>
    </Layout>
  );
}

export default App;
